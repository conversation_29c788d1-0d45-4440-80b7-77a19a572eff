{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}My Bookings - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            {% include 'components/provider_sidebar.html' %}
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Bookings Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'all' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=all">All Bookings</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'pending' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=pending">Pending</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'confirmed' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=confirmed">Confirmed</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'completed' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=completed">Completed</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'cancelled' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=cancelled">Cancelled</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    {% if bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Client</th>
                                        <th>Pet</th>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in bookings %}
                                    <tr>
                                        <td>{{ booking.user.get_full_name|default:booking.user.username }}</td>
                                        <td>{{ booking.pet.name }}</td>
                                        <td>{{ booking.service.name }}</td>
                                        <td>{{ booking.date|date:"M d, Y" }} at {{ booking.start_time|time:"g:i A" }}</td>
                                        <td>
                                            {% if booking.status == 'pending' %}
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            {% elif booking.status == 'confirmed' %}
                                                <span class="badge bg-success">Confirmed</span>
                                            {% elif booking.status == 'completed' %}
                                                <span class="badge bg-info">Completed</span>
                                            {% elif booking.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="dropdownMenuButton{{ booking.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Actions
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ booking.id }}">
                                                    <li><a class="dropdown-item" href="{% url 'booking-detail' booking.id %}">View Details</a></li>
                                                    {% if booking.status == 'pending' %}
                                                        <li>
                                                            <form action="{% url 'update-booking-status' booking.id %}" method="post">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="status" value="confirmed">
                                                                <button type="submit" class="dropdown-item text-success">Confirm Booking</button>
                                                            </form>
                                                        </li>
                                                        <li>
                                                            <form action="{% url 'update-booking-status' booking.id %}" method="post">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="status" value="cancelled">
                                                                <button type="submit" class="dropdown-item text-danger">Cancel Booking</button>
                                                            </form>
                                                        </li>
                                                    {% elif booking.status == 'confirmed' %}
                                                        <li>
                                                            <form action="{% url 'update-booking-status' booking.id %}" method="post">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="status" value="completed">
                                                                <button type="submit" class="dropdown-item text-info">Mark as Completed</button>
                                                            </form>
                                                        </li>
                                                        <li>
                                                            <form action="{% url 'update-booking-status' booking.id %}" method="post">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="status" value="cancelled">
                                                                <button type="submit" class="dropdown-item text-danger">Cancel Booking</button>
                                                            </form>
                                                        </li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item"><a class="page-link" href="?page={{ num }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}">{{ num }}</a></li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5>No Bookings Found</h5>
                            <p class="text-muted">
                                {% if booking_filter == 'all' %}
                                    You don't have any bookings yet.
                                {% else %}
                                    You don't have any {{ booking_filter }} bookings.
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
