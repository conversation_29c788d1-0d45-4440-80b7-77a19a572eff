{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Service - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="confirm-delete-container">
        <div class="confirm-delete-card">
            <div class="confirm-delete-header">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1>Delete Service</h1>
                <p>Are you sure you want to delete this service?</p>
            </div>

            <div class="service-preview">
                <div class="service-info">
                    <h3>{{ object.name }}</h3>
                    <p class="service-description">{{ object.description|truncatewords:30 }}</p>
                    <div class="service-meta">
                        <span class="service-price">${{ object.price }}</span>
                        <span class="service-duration">{{ object.duration }} minutes</span>
                        <span class="service-category">{{ object.category.name }}</span>
                    </div>
                </div>
            </div>

            <div class="warning-message">
                <div class="warning-content">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Warning:</strong> This action cannot be undone. Deleting this service will:
                        <ul>
                            <li>Remove the service from your profile permanently</li>
                            <li>Cancel any pending bookings for this service</li>
                            <li>Remove the service from search results</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="confirm-actions">
                <a href="{% url 'provider-detail' object.provider.pk %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Cancel
                </a>
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Delete Service
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.confirm-delete-container {
    max-width: 600px;
    margin: var(--spacing-4xl) auto;
    padding: var(--spacing-lg);
}

.confirm-delete-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2xl);
    border: 1px solid var(--gray-200);
}

.confirm-delete-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.warning-icon {
    width: 80px;
    height: 80px;
    background: var(--danger-light);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.warning-icon i {
    font-size: 2rem;
    color: var(--danger);
}

.confirm-delete-header h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-2xl);
}

.confirm-delete-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
    margin: 0;
}

.service-preview {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.service-info h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-xl);
}

.service-description {
    color: var(--gray-700);
    margin-bottom: var(--spacing-base);
    line-height: 1.6;
}

.service-meta {
    display: flex;
    gap: var(--spacing-base);
    flex-wrap: wrap;
}

.service-meta span {
    background: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-sm);
    font-weight: 500;
    border: 1px solid var(--gray-200);
}

.service-price {
    color: var(--primary);
}

.service-duration {
    color: var(--gray-600);
}

.service-category {
    color: var(--gray-600);
}

.warning-message {
    background: var(--warning-light);
    border: 1px solid var(--warning);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.warning-content {
    display: flex;
    gap: var(--spacing-base);
    align-items: flex-start;
}

.warning-content i {
    color: var(--warning-dark);
    font-size: var(--font-lg);
    margin-top: 2px;
    flex-shrink: 0;
}

.warning-content div {
    color: var(--warning-dark);
}

.warning-content strong {
    display: block;
    margin-bottom: var(--spacing-xs);
}

.warning-content ul {
    margin: var(--spacing-sm) 0 0 var(--spacing-base);
    padding: 0;
}

.warning-content li {
    margin-bottom: var(--spacing-xs);
}

.confirm-actions {
    display: flex;
    gap: var(--spacing-base);
    justify-content: center;
}

.confirm-actions .btn {
    min-width: 120px;
}

@media (max-width: 768px) {
    .confirm-delete-container {
        margin: var(--spacing-xl) auto;
        padding: var(--spacing-base);
    }
    
    .confirm-delete-card {
        padding: var(--spacing-lg);
    }
    
    .service-meta {
        flex-direction: column;
    }
    
    .confirm-actions {
        flex-direction: column;
    }
    
    .confirm-actions .btn {
        width: 100%;
    }
}
</style>
{% endblock %}
