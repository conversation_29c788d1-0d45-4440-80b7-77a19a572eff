{% extends 'base.html' %}
{% load static %}

{% block title %}Book Service - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="booking-header">
        <a href="{% url 'provider-detail' service.provider.pk %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Provider
        </a>
        <h1>Book Service</h1>
    </div>

    <div class="booking-container">
        <div class="service-summary">
            <div class="service-card">
                <h2>{{ service.name }}</h2>
                <div class="provider-info">
                    <div class="provider-avatar">
                        {% if service.provider.profile_picture %}
                            <img src="{{ service.provider.profile_picture.url }}" alt="{{ service.provider.user.username }}">
                        {% else %}
                            <div class="avatar-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="provider-details">
                        <h3>{{ service.provider.user.full_name|default:service.provider.user.email }}</h3>
                        <div class="provider-rating">
                            <div class="stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= service.provider.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="rating-text">{{ service.provider.rating|floatformat:1 }}</span>
                        </div>
                    </div>
                </div>

                <div class="service-details">
                    <p class="service-description">{{ service.description }}</p>
                    <div class="service-price">
                        <span class="price-label">Price:</span>
                        <span class="price-value">₹{{ service.price }}</span>
                    </div>
                    <div class="service-duration">
                        <span class="duration-label">Duration:</span>
                        <span class="duration-value">{{ service.duration }} minutes</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="booking-form-container">
            <div class="form-card">
                <h3>Booking Details</h3>
                <form method="post" class="booking-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="form-errors">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="form-group">
                        {{ form.date.label_tag }}
                        {{ form.date }}
                        {% if form.date.errors %}
                            <div class="form-errors">
                                {{ form.date.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.start_time.label_tag }}
                        {{ form.start_time }}
                        {% if form.start_time.errors %}
                            <div class="form-errors">
                                {{ form.start_time.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.pet.label_tag }}
                        {{ form.pet }}
                        {% if form.pet.errors %}
                            <div class="form-errors">
                                {{ form.pet.errors }}
                            </div>
                        {% endif %}
                        {% if not form.pet.field.queryset %}
                            <div class="form-errors">
                                You need to add a pet to your profile before booking a service.
                                <a href="{% url 'add-pet' %}">Add a pet here</a>.
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.notes.label_tag }}
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="form-errors">
                                {{ form.notes.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="price-calculation">
                        <div class="calculation-row">
                            <span>Service Price:</span>
                            <span id="service-rate">${{ service.price }}</span>
                        </div>
                        <div class="calculation-row">
                            <span>Duration:</span>
                            <span id="selected-duration">{{ service.duration }} minutes</span>
                        </div>
                        <div class="calculation-row total">
                            <span>Total Price:</span>
                            <span id="total-price">${{ service.price }}</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a href="{% url 'provider-detail' service.provider.pk %}" class="btn btn-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calendar-check"></i>
                            Book Service
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.booking-header {
    margin-bottom: var(--spacing-xl);
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary);
    text-decoration: none;
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    transition: var(--transition-base);
}

.back-link:hover {
    color: var(--primary-dark);
}

.booking-header h1 {
    color: var(--gray-800);
    margin: 0;
}

.booking-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
}

.service-card, .form-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-xl);
}

.service-card h2 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-2xl);
}

.provider-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.provider-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    overflow: hidden;
    background: var(--gray-200);
}

.provider-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: var(--font-xl);
}

.provider-details h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.provider-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stars {
    color: var(--warning);
}

.rating-text {
    color: var(--gray-600);
    font-size: var(--font-sm);
}

.service-details {
    display: grid;
    gap: var(--spacing-base);
}

.service-description {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.service-price, .service-duration {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-100);
}

.price-label, .duration-label {
    color: var(--gray-600);
    font-weight: 500;
}

.price-value {
    color: var(--primary);
    font-weight: 600;
    font-size: var(--font-lg);
}

.duration-value {
    color: var(--gray-800);
    font-weight: 600;
}

.form-card h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-xl);
}

.booking-form {
    display: grid;
    gap: var(--spacing-lg);
}

.form-group {
    display: grid;
    gap: var(--spacing-xs);
}

.form-group label {
    color: var(--gray-700);
    font-weight: 500;
    font-size: var(--font-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-errors {
    color: var(--danger);
    font-size: var(--font-sm);
}

.price-calculation {
    background: var(--gray-50);
    padding: var(--spacing-base);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.calculation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.calculation-row.total {
    border-top: 1px solid var(--gray-300);
    margin-top: var(--spacing-xs);
    padding-top: var(--spacing-sm);
    font-weight: 600;
    color: var(--primary);
    font-size: var(--font-lg);
}

.form-actions {
    display: flex;
    gap: var(--spacing-r);
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .booking-container {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const durationSelect = document.querySelector('#id_duration');
    const servicePrice = {{ service.price }};

    if (durationSelect) {
        durationSelect.addEventListener('change', function() {
            const duration = parseFloat(this.value) || {{ service.duration }};
            // For services, the price is typically fixed, not hourly
            // But if you want to calculate based on duration, you can modify this logic
            const totalPrice = servicePrice.toFixed(2);

            document.getElementById('selected-duration').textContent = duration + ' minutes';
            document.getElementById('total-price').textContent = '$' + totalPrice;
        });
    }
});
</script>
{% endblock %}
