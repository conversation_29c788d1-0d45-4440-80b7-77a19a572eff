{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if form.instance.id %}Edit Service{% else %}Add New Service{% endif %} - PetPaw
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">{% if form.instance.id %}Edit Service{% else %}Add New Service{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="service-form">
                        {% csrf_token %}

                        <!-- Service Type Selection -->
                        <div class="mb-4">
                            <label class="form-label">Service Type</label>
                            <div class="service-type-selection">
                                {% for choice in form.service_type %}
                                    <div class="form-check">
                                        {{ choice.tag }}
                                        <label class="form-check-label" for="{{ choice.id_for_label }}">
                                            {{ choice.choice_label }}
                                        </label>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Predefined Service Selection -->
                        <div id="predefined-service-section" class="mb-4">
                            <label for="{{ form.predefined_service.id_for_label }}" class="form-label">Choose a Service</label>
                            {{ form.predefined_service }}
                            <div class="form-text">Select from our predefined services with suggested pricing and duration.</div>

                            <!-- Predefined Service Preview -->
                            <div id="predefined-service-preview" class="mt-3" style="display: none;">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title" id="preview-name"></h6>
                                        <p class="card-text" id="preview-description"></p>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">Suggested Price:</small>
                                                <div id="preview-price" class="fw-bold"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">Suggested Duration:</small>
                                                <div id="preview-duration" class="fw-bold"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Service Fields -->
                        <div id="custom-service-section">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.category|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.name|as_crispy_field }}
                                </div>
                            </div>

                            {{ form.description|as_crispy_field }}

                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.price|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.duration|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        {{ form.is_available|as_crispy_field }}

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'provider-services' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> {% if form.instance.id %}Update{% else %}Create{% endif %} Service
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const serviceTypeRadios = document.querySelectorAll('input[name="service_type"]');
    const predefinedSection = document.getElementById('predefined-service-section');
    const customSection = document.getElementById('custom-service-section');
    const predefinedSelect = document.getElementById('{{ form.predefined_service.id_for_label }}');
    const previewDiv = document.getElementById('predefined-service-preview');
    const categorySelect = document.getElementById('{{ form.category.id_for_label }}');

    // Store all predefined services
    let allPredefinedServices = {};

    // Load predefined services from API
    async function loadPredefinedServices(categoryId = null) {
        try {
            const url = categoryId ?
                `{% url 'api-predefined-services' %}?category_id=${categoryId}` :
                `{% url 'api-predefined-services' %}`;

            const response = await fetch(url);
            const data = await response.json();

            // Clear existing options except the empty option
            predefinedSelect.innerHTML = '<option value="">Select a service...</option>';

            // Add new options
            data.services.forEach(service => {
                allPredefinedServices[service.id] = service;
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = service.name;
                predefinedSelect.appendChild(option);
            });

        } catch (error) {
            console.error('Error loading predefined services:', error);
        }
    }

    function toggleSections() {
        const selectedType = document.querySelector('input[name="service_type"]:checked').value;

        if (selectedType === 'predefined') {
            predefinedSection.style.display = 'block';
            customSection.style.display = 'none';

            // Make predefined fields required
            predefinedSelect.required = true;

            // Make custom fields not required
            document.getElementById('{{ form.name.id_for_label }}').required = false;
            document.getElementById('{{ form.description.id_for_label }}').required = false;
            document.getElementById('{{ form.category.id_for_label }}').required = false;
            document.getElementById('{{ form.price.id_for_label }}').required = false;
            document.getElementById('{{ form.duration.id_for_label }}').required = false;

        } else {
            predefinedSection.style.display = 'none';
            customSection.style.display = 'block';
            previewDiv.style.display = 'none';

            // Make custom fields required
            document.getElementById('{{ form.name.id_for_label }}').required = true;
            document.getElementById('{{ form.description.id_for_label }}').required = true;
            document.getElementById('{{ form.category.id_for_label }}').required = true;
            document.getElementById('{{ form.price.id_for_label }}').required = true;
            document.getElementById('{{ form.duration.id_for_label }}').required = true;

            // Make predefined fields not required
            predefinedSelect.required = false;
        }
    }

    function showPreview() {
        const selectedId = predefinedSelect.value;

        if (selectedId && allPredefinedServices[selectedId]) {
            const service = allPredefinedServices[selectedId];

            document.getElementById('preview-name').textContent = service.name;
            document.getElementById('preview-description').textContent = service.description;
            document.getElementById('preview-price').textContent = '$' + service.suggested_price;
            document.getElementById('preview-duration').textContent = service.suggested_duration + ' minutes';

            previewDiv.style.display = 'block';
        } else {
            previewDiv.style.display = 'none';
        }
    }

    // Event listeners
    serviceTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleSections();
            if (this.value === 'predefined') {
                loadPredefinedServices();
            }
        });
    });

    predefinedSelect.addEventListener('change', showPreview);

    // Load services when category changes (for custom services)
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            const selectedType = document.querySelector('input[name="service_type"]:checked').value;
            if (selectedType === 'predefined') {
                loadPredefinedServices(this.value);
            }
        });
    }

    // Initial setup
    toggleSections();
    loadPredefinedServices();
    showPreview();
});
</script>
{% endblock %}
