{% extends 'base.html' %}

{% block title %}
    {% if form.instance.id %}Edit Service{% else %}Add New Service{% endif %} - PetPaw
{% endblock %}

{% block content %}
<div class="container">
    <div class="service-form-container">
        <div class="service-form-header">
            <h1>{% if form.instance.id %}Edit Service{% else %}Add New Service{% endif %}</h1>
            <p>{% if form.instance.id %}Update your service details{% else %}Create a new service offering for your clients{% endif %}</p>
        </div>

        <div class="service-form-card">
            <form method="post" enctype="multipart/form-data" id="service-form">
                {% csrf_token %}

                <!-- Service Type Selection -->
                <div class="form-section">
                    <h3>Service Type</h3>
                    <div class="service-type-selection">
                        {% for choice in form.service_type %}
                            <div class="radio-option">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}">
                                    <span class="radio-label">{{ choice.choice_label }}</span>
                                    <span class="radio-description">
                                        {% if choice.choice_value == 'predefined' %}
                                            Choose from our curated list of services with suggested pricing
                                        {% else %}
                                            Create your own custom service with your own pricing and details
                                        {% endif %}
                                    </span>
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Predefined Service Selection -->
                <div id="predefined-service-section" class="form-section">
                    <h3>Choose a Service</h3>
                    <div class="form-group">
                        <label for="{{ form.predefined_service.id_for_label }}">Select Service</label>
                        {{ form.predefined_service }}
                        <div class="form-help">Select from our predefined services with suggested pricing and duration.</div>
                    </div>

                    <!-- Predefined Service Preview -->
                    <div id="predefined-service-preview" class="service-preview" style="display: none;">
                        <div class="preview-card">
                            <h4 id="preview-name"></h4>
                            <p id="preview-description"></p>
                            <div class="preview-details">
                                <div class="preview-item">
                                    <span class="preview-label">Suggested Price:</span>
                                    <span id="preview-price" class="preview-value"></span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Suggested Duration:</span>
                                    <span id="preview-duration" class="preview-value"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Service Fields -->
                <div id="custom-service-section" class="form-section">
                    <h3>Service Details</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.category.id_for_label }}">Category</label>
                            {{ form.category }}
                        </div>
                        <div class="form-group">
                            <label for="{{ form.name.id_for_label }}">Service Name</label>
                            {{ form.name }}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}">Description</label>
                        {{ form.description }}
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.price.id_for_label }}">Price ($)</label>
                            {{ form.price }}
                        </div>
                        <div class="form-group">
                            <label for="{{ form.duration.id_for_label }}">Duration (minutes)</label>
                            {{ form.duration }}
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-group checkbox-group">
                        {{ form.is_available }}
                        <label for="{{ form.is_available.id_for_label }}">Service is available for booking</label>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="{% url 'provider-services' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        {% if form.instance.id %}Update Service{% else %}Create Service{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.service-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
}

.service-form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.service-form-header h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.service-form-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
}

.service-form-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-base);
    padding: var(--spacing-2xl);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
}

.form-section h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--gray-200);
}

.service-type-selection {
    display: grid;
    gap: var(--spacing-base);
}

.radio-option {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    transition: var(--transition-base);
    cursor: pointer;
}

.radio-option:hover {
    border-color: var(--primary-light);
    background: var(--primary-light);
}

.radio-option input[type="radio"] {
    margin-right: var(--spacing-sm);
}

.radio-option input[type="radio"]:checked + label {
    color: var(--primary);
}

.radio-option:has(input[type="radio"]:checked) {
    border-color: var(--primary);
    background: var(--primary-light);
}

.radio-label {
    display: block;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.radio-description {
    display: block;
    font-size: var(--font-sm);
    color: var(--gray-600);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-help {
    font-size: var(--font-sm);
    color: var(--gray-600);
    margin-top: var(--spacing-xs);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

.service-preview {
    margin-top: var(--spacing-lg);
}

.preview-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
}

.preview-card h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.preview-card p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-base);
}

.preview-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-base);
}

.preview-item {
    display: flex;
    flex-direction: column;
}

.preview-label {
    font-size: var(--font-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xs);
}

.preview-value {
    font-weight: 600;
    color: var(--primary);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-base);
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .preview-details {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const serviceTypeRadios = document.querySelectorAll('input[name="service_type"]');
    const predefinedSection = document.getElementById('predefined-service-section');
    const customSection = document.getElementById('custom-service-section');
    const predefinedSelect = document.getElementById('{{ form.predefined_service.id_for_label }}');
    const previewDiv = document.getElementById('predefined-service-preview');
    const categorySelect = document.getElementById('{{ form.category.id_for_label }}');

    // Store all predefined services
    let allPredefinedServices = {};

    // Load predefined services from API
    async function loadPredefinedServices(categoryId = null) {
        try {
            const url = categoryId ?
                `{% url 'api-predefined-services' %}?category_id=${categoryId}` :
                `{% url 'api-predefined-services' %}`;

            const response = await fetch(url);
            const data = await response.json();

            // Clear existing options except the empty option
            predefinedSelect.innerHTML = '<option value="">Select a service...</option>';

            // Add new options
            data.services.forEach(service => {
                allPredefinedServices[service.id] = service;
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = service.name;
                predefinedSelect.appendChild(option);
            });

        } catch (error) {
            console.error('Error loading predefined services:', error);
        }
    }

    function toggleSections() {
        const selectedType = document.querySelector('input[name="service_type"]:checked')?.value;

        if (selectedType === 'predefined') {
            predefinedSection.style.display = 'block';
            customSection.style.display = 'none';

            // Make predefined fields required
            if (predefinedSelect) predefinedSelect.required = true;

            // Make custom fields not required
            const nameField = document.getElementById('{{ form.name.id_for_label }}');
            const descField = document.getElementById('{{ form.description.id_for_label }}');
            const catField = document.getElementById('{{ form.category.id_for_label }}');
            const priceField = document.getElementById('{{ form.price.id_for_label }}');
            const durationField = document.getElementById('{{ form.duration.id_for_label }}');

            if (nameField) nameField.required = false;
            if (descField) descField.required = false;
            if (catField) catField.required = false;
            if (priceField) priceField.required = false;
            if (durationField) durationField.required = false;

        } else {
            predefinedSection.style.display = 'none';
            customSection.style.display = 'block';
            previewDiv.style.display = 'none';

            // Make custom fields required
            const nameField = document.getElementById('{{ form.name.id_for_label }}');
            const descField = document.getElementById('{{ form.description.id_for_label }}');
            const catField = document.getElementById('{{ form.category.id_for_label }}');
            const priceField = document.getElementById('{{ form.price.id_for_label }}');
            const durationField = document.getElementById('{{ form.duration.id_for_label }}');

            if (nameField) nameField.required = true;
            if (descField) descField.required = true;
            if (catField) catField.required = true;
            if (priceField) priceField.required = true;
            if (durationField) durationField.required = true;

            // Make predefined fields not required
            if (predefinedSelect) predefinedSelect.required = false;
        }
    }

    function showPreview() {
        const selectedId = predefinedSelect?.value;

        if (selectedId && allPredefinedServices[selectedId]) {
            const service = allPredefinedServices[selectedId];

            const nameEl = document.getElementById('preview-name');
            const descEl = document.getElementById('preview-description');
            const priceEl = document.getElementById('preview-price');
            const durationEl = document.getElementById('preview-duration');

            if (nameEl) nameEl.textContent = service.name;
            if (descEl) descEl.textContent = service.description;
            if (priceEl) priceEl.textContent = '$' + service.suggested_price;
            if (durationEl) durationEl.textContent = service.suggested_duration + ' minutes';

            previewDiv.style.display = 'block';
        } else {
            previewDiv.style.display = 'none';
        }
    }

    // Event listeners
    serviceTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleSections();
            if (this.value === 'predefined') {
                loadPredefinedServices();
            }
        });
    });

    if (predefinedSelect) {
        predefinedSelect.addEventListener('change', showPreview);
    }

    // Load services when category changes (for custom services)
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            const selectedType = document.querySelector('input[name="service_type"]:checked')?.value;
            if (selectedType === 'predefined') {
                loadPredefinedServices(this.value);
            }
        });
    }

    // Initial setup
    toggleSections();
    loadPredefinedServices();
    showPreview();
});
</script>
{% endblock %}
